/* Modern Events Page Styles */
.events-page {
  padding-top: 4rem; /* Account for fixed header */
}

.events-header {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
  padding: var(--space-20) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.events-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(124, 109, 242, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(217, 70, 239, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.page-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: var(--space-4);
  font-weight: 800;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  position: relative;
  z-index: 1;
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
}

/* Search and Filters */
.search-filters {
  padding: 2rem 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 2rem;
}

.search-bar {
  display: flex;
  max-width: 500px;
  margin: 0 auto 2rem;
  position: relative;
}

.search-input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: 2px solid #ddd;
  border-radius: 50px;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: #667eea;
  color: white;
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: #5a6fd8;
}

.category-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 2px solid #ddd;
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.category-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.category-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.category-icon {
  font-size: 1.2rem;
}

/* Events Section */
.events-section {
  padding: 1rem 0;
}

.events-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.events-header-info h2 {
  font-size: 2rem;
  color: #333;
}

.events-count {
  color: #666;
  font-size: 1rem;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.event-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.event-image {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.event-emoji {
  font-size: 4rem;
}

.event-category-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.event-content {
  padding: 1.5rem;
}

.event-title {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  color: #333;
  font-weight: 600;
}

.event-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
  font-size: 0.95rem;
}

.event-details {
  margin-bottom: 1.5rem;
}

.event-date,
.event-location {
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.event-price {
  font-weight: 600;
  color: #667eea;
  font-size: 1.2rem;
}

.book-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.book-btn:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

/* No Events State */
.no-events {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-events-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-events h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .events-header-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .events-header-info h2 {
    font-size: 1.5rem;
  }

  .category-filters {
    gap: 0.5rem;
  }

  .category-btn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  .events-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .search-bar {
    margin: 0 0 1.5rem;
  }
}

@media (max-width: 480px) {
  .category-filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .category-btn {
    flex-shrink: 0;
  }
}
