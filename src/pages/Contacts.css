/* Modern Contacts Page Styles */
.contacts-page {
  padding-top: 4rem; /* Account for fixed header */
}

.contacts-header {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
  padding: var(--space-20) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.contacts-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(124, 109, 242, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(217, 70, 239, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.page-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: var(--space-4);
  font-weight: 800;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  position: relative;
  z-index: 1;
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
}

/* Contact Content */
.contacts-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  padding: 4rem 0;
}

.contact-info h2,
.contact-form-section h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.contact-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Contact Methods */
.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.contact-method:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.contact-icon {
  font-size: 2rem;
  background: #667eea;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-details h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.contact-details p {
  color: #333;
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.contact-details span {
  color: #666;
  font-size: 0.9rem;
}

/* Social Section */
.social-section h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.social-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  padding: 0.8rem;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.social-link:hover {
  background: #667eea;
  color: white;
  transform: translateX(5px);
}

/* Contact Form */
.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* FAQ Section */
.faq-section {
  padding: 4rem 0;
  border-top: 1px solid #eee;
  margin-top: 2rem;
}

.faq-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.faq-item h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
}

.faq-item p {
  color: #666;
  line-height: 1.6;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .contacts-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
  }

  .contact-icon {
    align-self: center;
  }

  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .social-link {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .faq-section {
    padding: 2rem 0;
  }

  .faq-section h2 {
    font-size: 2rem;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contacts-header {
    padding: 2rem 0;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .contact-form {
    padding: 1rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.8rem;
  }

  .submit-btn {
    padding: 0.8rem 1.5rem;
  }
}
