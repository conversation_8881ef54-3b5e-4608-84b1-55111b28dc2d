/* Modern Signup Page Styles */
.signup-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--secondary-50) 0%, var(--primary-50) 100%);
  padding: var(--space-4);
  position: relative;
  overflow: hidden;
}

.back-to-home {
  position: absolute;
  top: var(--space-6);
  left: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  z-index: 10;
}

.back-to-home:hover {
  color: var(--secondary-600);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.signup-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(217, 70, 239, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 109, 242, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.signup-container {
  background: white;
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  max-width: 1100px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 700px;
  position: relative;
  z-index: 1;
}

.signup-content {
  padding: var(--space-10);
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
}

.signup-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.logo-section {
  margin-bottom: var(--space-6);
}

.logo-icon {
  margin: 0 auto var(--space-4);
  width: 64px;
  height: 64px;
  background: var(--secondary-50);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.signup-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  letter-spacing: -0.02em;
}

.signup-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-input {
  padding: var(--space-4);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: all var(--transition-fast);
  background: white;
}

.form-input:focus {
  border-color: var(--secondary-500);
  box-shadow: 0 0 0 3px rgba(217, 70, 239, 0.1);
  outline: none;
}

.form-input.error {
  border-color: var(--red-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  color: var(--red-500);
  font-size: 0.875rem;
  margin-top: var(--space-1);
}

.form-options {
  margin: var(--space-2) 0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: var(--secondary-600);
  margin-top: 2px;
  flex-shrink: 0;
}

.checkbox-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.link {
  color: var(--secondary-600);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.link:hover {
  color: var(--secondary-700);
}

.signup-btn {
  background: var(--secondary-600);
  color: white;
  border: none;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.signup-btn:hover:not(:disabled) {
  background: var(--secondary-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.signup-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.signup-footer {
  text-align: center;
  margin-top: var(--space-6);
}

.login-prompt {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.login-link {
  color: var(--secondary-600);
  text-decoration: none;
  font-weight: 600;
  margin-left: var(--space-1);
  transition: color var(--transition-fast);
}

.login-link:hover {
  color: var(--secondary-700);
}

.signup-visual {
  background: linear-gradient(135deg, var(--secondary-600) 0%, var(--primary-600) 100%);
  color: white;
  padding: var(--space-12);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.signup-visual::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.visual-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.visual-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: 1.3;
}

.visual-content p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin-bottom: var(--space-8);
  line-height: 1.6;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 800;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  text-align: left;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 1rem;
  font-weight: 500;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .signup-container {
    grid-template-columns: 1fr;
    max-width: 400px;
    min-height: auto;
  }

  .signup-visual {
    display: none;
  }

  .signup-content {
    padding: var(--space-8);
  }

  .signup-title {
    font-size: 1.75rem;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
  }

  .stat-number {
    font-size: 1.25rem;
  }
}
