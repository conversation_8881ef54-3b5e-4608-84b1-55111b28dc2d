import React, { useState } from 'react';
import './Events.css';

const Events = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const events = [
    {
      id: 1,
      title: "Summer Music Festival 2024",
      date: "July 15-17, 2024",
      location: "Central Park, NYC",
      price: "From $89",
      image: "🎵",
      category: "music",
      description: "Three days of amazing music with top artists from around the world."
    },
    {
      id: 2,
      title: "Tech Innovation Conference",
      date: "August 22, 2024",
      location: "Convention Center, SF",
      price: "From $299",
      image: "💻",
      category: "conference",
      description: "Discover the latest in technology and innovation."
    },
    {
      id: 3,
      title: "Broadway Musical Night",
      date: "September 5, 2024",
      location: "Theater District, NYC",
      price: "From $125",
      image: "🎭",
      category: "theater",
      description: "An unforgettable evening of Broadway's finest performances."
    },
    {
      id: 4,
      title: "NBA Championship Game",
      date: "June 10, 2024",
      location: "Madison Square Garden",
      price: "From $450",
      image: "🏀",
      category: "sports",
      description: "Witness basketball history in the making."
    },
    {
      id: 5,
      title: "Jazz & Blues Festival",
      date: "August 5-7, 2024",
      location: "Riverside Park, Chicago",
      price: "From $65",
      image: "🎷",
      category: "music",
      description: "Smooth jazz and soulful blues under the stars."
    },
    {
      id: 6,
      title: "Art & Design Expo",
      date: "September 15-17, 2024",
      location: "Art Museum, LA",
      price: "From $35",
      image: "🎨",
      category: "art",
      description: "Explore contemporary art and innovative design."
    }
  ];

  const categories = [
    { id: 'all', name: 'All Events', icon: '🎪' },
    { id: 'music', name: 'Music', icon: '🎵' },
    { id: 'sports', name: 'Sports', icon: '⚽' },
    { id: 'theater', name: 'Theater', icon: '🎭' },
    { id: 'conference', name: 'Conference', icon: '💼' },
    { id: 'art', name: 'Art', icon: '🎨' }
  ];

  const filteredEvents = events.filter(event => {
    const matchesCategory = selectedCategory === 'all' || event.category === selectedCategory;
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.location.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="events-page">
      <div className="events-header">
        <div className="container">
          <h1 className="page-title">Discover Amazing Events</h1>
          <p className="page-subtitle">Find the perfect event for you</p>
        </div>
      </div>

      <div className="container">
        {/* Search and Filters */}
        <div className="search-filters">
          <div className="search-bar">
            <input
              type="text"
              placeholder="Search events, locations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <button className="search-btn">🔍</button>
          </div>

          <div className="category-filters">
            {categories.map(category => (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <span className="category-icon">{category.icon}</span>
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Events Grid */}
        <div className="events-section">
          <div className="events-header-info">
            <h2>
              {selectedCategory === 'all' ? 'All Events' : 
               categories.find(c => c.id === selectedCategory)?.name}
            </h2>
            <span className="events-count">
              {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} found
            </span>
          </div>

          <div className="events-grid">
            {filteredEvents.map(event => (
              <div key={event.id} className="event-card">
                <div className="event-image">
                  <span className="event-emoji">{event.image}</span>
                  <div className="event-category-badge">
                    {categories.find(c => c.id === event.category)?.name}
                  </div>
                </div>
                <div className="event-content">
                  <h3 className="event-title">{event.title}</h3>
                  <p className="event-description">{event.description}</p>
                  <div className="event-details">
                    <p className="event-date">📅 {event.date}</p>
                    <p className="event-location">📍 {event.location}</p>
                  </div>
                  <div className="event-footer">
                    <span className="event-price">{event.price}</span>
                    <button className="book-btn">Book Tickets</button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredEvents.length === 0 && (
            <div className="no-events">
              <div className="no-events-icon">🔍</div>
              <h3>No events found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Events;
