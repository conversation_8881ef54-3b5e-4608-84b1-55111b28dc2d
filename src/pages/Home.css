/* Modern Home Page Styles */
.home {
  padding-top: 4rem; /* Account for fixed header */
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
  padding: var(--space-24) 0 var(--space-32);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(124, 109, 242, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(217, 70, 239, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-content {
  max-width: 600px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(124, 109, 242, 0.1);
  border: 1px solid rgba(124, 109, 242, 0.2);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  margin-bottom: var(--space-6);
}

.badge-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--primary-700);
}

.hero-title {
  font-size: clamp(3rem, 6vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.hero-highlight {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
}

.cta-primary {
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-xl);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.cta-primary:hover {
  background: var(--primary-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.cta-secondary {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-xl);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.cta-secondary:hover {
  background: var(--gray-50);
  border-color: var(--primary-300);
  color: var(--primary-600);
}

.hero-stats {
  display: flex;
  gap: var(--space-8);
}

.stat {
  text-align: left;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

.hero-visual {
  position: relative;
  height: 500px;
}

.hero-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
  animation: float 6s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  right: 20%;
  animation-delay: 2s;
}

.card-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

.card-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-icon {
  font-size: 1.5rem;
}

.card-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.card-subtitle {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Features Section */
.features {
  padding: var(--space-24) 0;
  background: var(--background);
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.features-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-badge {
  display: inline-block;
  background: var(--primary-50);
  color: var(--primary-700);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: var(--space-4);
  border: 1px solid var(--primary-200);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.section-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
}

.feature-card {
  background: white;
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
  transform-origin: left;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-50);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
  color: var(--primary-600);
  transition: all var(--transition-fast);
}

.feature-card:hover .feature-icon {
  background: var(--primary-100);
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 0.95rem;
}

/* Featured Events Section */
.featured-events {
  padding: var(--space-24) 0;
  background: var(--background-secondary);
}

.events-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.event-card {
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  group: hover;
}

.event-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.event-image {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.event-card:hover .event-image img {
  transform: scale(1.05);
}

.event-category {
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.event-overlay {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.event-card:hover .event-overlay {
  opacity: 1;
}

.quick-view-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quick-view-btn:hover {
  background: white;
  transform: scale(1.1);
}

.event-content {
  padding: var(--space-6);
}

.event-meta {
  margin-bottom: var(--space-3);
}

.event-attendees {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  background: var(--gray-100);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-weight: 500;
}

.event-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
  line-height: 1.3;
}

.event-details {
  margin-bottom: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.event-date,
.event-location {
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.event-price {
  font-weight: 700;
  color: var(--primary-600);
  font-size: 1.125rem;
}

.book-btn {
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.book-btn:hover {
  background: var(--primary-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.view-all {
  text-align: center;
}

.view-all-btn {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-xl);
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.view-all-btn:hover {
  background: var(--primary-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Newsletter Section */
.newsletter {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
  color: white;
  padding: var(--space-24) 0;
  position: relative;
  overflow: hidden;
}

.newsletter::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.newsletter-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: center;
  position: relative;
  z-index: 1;
}

.newsletter-text h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: 1.2;
}

.newsletter-text p {
  font-size: 1.125rem;
  opacity: 0.9;
  line-height: 1.7;
}

.newsletter-form {
  max-width: 500px;
}

.form-group {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.email-input {
  flex: 1;
  padding: var(--space-4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  transition: all var(--transition-fast);
}

.email-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.email-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.subscribe-btn {
  background: white;
  color: var(--primary-600);
  border: none;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-xl);
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  white-space: nowrap;
}

.subscribe-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.newsletter-disclaimer {
  font-size: 0.875rem;
  opacity: 0.7;
  margin: 0;
}

/* Mobile Styles */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }

  .hero-visual {
    order: -1;
    height: 300px;
  }

  .hero-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: var(--space-16) 0 var(--space-20);
  }

  .hero-container {
    padding: 0 var(--space-4);
  }

  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .events-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .newsletter-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
    text-align: center;
  }

  .form-group {
    flex-direction: column;
  }

  .email-input,
  .subscribe-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-4);
  }

  .floating-card {
    display: none;
  }

  .hero-visual {
    height: 200px;
  }
}
