import React from 'react';
import './Home.css';

const Home = () => {
  const featuredEvents = [
    {
      id: 1,
      title: "Summer Music Festival 2024",
      date: "July 15-17, 2024",
      location: "Central Park, NYC",
      price: "From $89",
      image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop",
      category: "Music",
      attendees: "12K+ attending"
    },
    {
      id: 2,
      title: "Tech Innovation Conference",
      date: "August 22, 2024",
      location: "Convention Center, SF",
      price: "From $299",
      image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop",
      category: "Conference",
      attendees: "5K+ attending"
    },
    {
      id: 3,
      title: "Broadway Musical Night",
      date: "September 5, 2024",
      location: "Theater District, NYC",
      price: "From $125",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",
      category: "Theater",
      attendees: "2K+ attending"
    },
    {
      id: 4,
      title: "Food & Wine Festival",
      date: "October 10-12, 2024",
      location: "Napa Valley, CA",
      price: "From $199",
      image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop",
      category: "Food",
      attendees: "8K+ attending"
    }
  ];

  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-container">
          <div className="hero-content">
            <div className="hero-badge">
              <span className="badge-text">✨ New Platform Launch</span>
            </div>
            <h1 className="hero-title">
              Discover Extraordinary
              <span className="hero-highlight"> Events</span>
            </h1>
            <p className="hero-description">
              Connect with amazing experiences. From intimate concerts to grand festivals,
              find and book tickets for events that inspire and entertain.
            </p>
            <div className="hero-actions">
              <button className="cta-primary">
                <span>Explore Events</span>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="m9 18 6-6-6-6"/>
                </svg>
              </button>
              <button className="cta-secondary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polygon points="5,3 19,12 5,21"/>
                </svg>
                <span>Watch Demo</span>
              </button>
            </div>
            <div className="hero-stats">
              <div className="stat">
                <div className="stat-number">50K+</div>
                <div className="stat-label">Events Listed</div>
              </div>
              <div className="stat">
                <div className="stat-number">1M+</div>
                <div className="stat-label">Tickets Sold</div>
              </div>
              <div className="stat">
                <div className="stat-number">500+</div>
                <div className="stat-label">Cities</div>
              </div>
            </div>
          </div>
          <div className="hero-visual">
            <div className="hero-image">
              <div className="floating-card card-1">
                <div className="card-content">
                  <div className="card-icon">🎵</div>
                  <div className="card-text">
                    <div className="card-title">Music Festival</div>
                    <div className="card-subtitle">This Weekend</div>
                  </div>
                </div>
              </div>
              <div className="floating-card card-2">
                <div className="card-content">
                  <div className="card-icon">🎭</div>
                  <div className="card-text">
                    <div className="card-title">Theater Show</div>
                    <div className="card-subtitle">Broadway</div>
                  </div>
                </div>
              </div>
              <div className="floating-card card-3">
                <div className="card-content">
                  <div className="card-icon">🏀</div>
                  <div className="card-text">
                    <div className="card-title">Sports Event</div>
                    <div className="card-subtitle">Live Game</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <div className="features-header">
            <div className="section-badge">Features</div>
            <h2 className="section-title">Everything you need to discover events</h2>
            <p className="section-description">
              Powerful features designed to make event discovery and booking seamless
            </p>
          </div>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 12l2 2 4-4"/>
                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                  <path d="M3 12h6m6 0h6"/>
                </svg>
              </div>
              <h3>Instant Booking</h3>
              <p>Book tickets in seconds with our streamlined checkout process. No waiting, no hassle.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                  <circle cx="12" cy="16" r="1"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
              </div>
              <h3>Secure Payments</h3>
              <p>Bank-level security with encrypted transactions. Your payment information is always protected.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
                  <line x1="12" y1="18" x2="12.01" y2="18"/>
                </svg>
              </div>
              <h3>Digital Tickets</h3>
              <p>Instant digital tickets delivered to your phone. No printing required, just show and go.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
              </div>
              <h3>Personalized</h3>
              <p>AI-powered recommendations based on your preferences and location. Discover events you'll love.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
              </div>
              <h3>Real-time Updates</h3>
              <p>Get instant notifications about event changes, new releases, and exclusive offers.</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
              </div>
              <h3>24/7 Support</h3>
              <p>Round-the-clock customer support to help you with any questions or issues.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Events Section */}
      <section className="featured-events">
        <div className="container">
          <div className="events-header">
            <div className="section-badge">Trending</div>
            <h2 className="section-title">Featured Events</h2>
            <p className="section-description">
              Discover the most popular events happening near you
            </p>
          </div>
          <div className="events-grid">
            {featuredEvents.map(event => (
              <div key={event.id} className="event-card">
                <div className="event-image">
                  <img src={event.image} alt={event.title} />
                  <div className="event-category">{event.category}</div>
                  <div className="event-overlay">
                    <button className="quick-view-btn">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="event-content">
                  <div className="event-meta">
                    <span className="event-attendees">{event.attendees}</span>
                  </div>
                  <h3 className="event-title">{event.title}</h3>
                  <div className="event-details">
                    <div className="event-date">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                        <line x1="16" y1="2" x2="16" y2="6"/>
                        <line x1="8" y1="2" x2="8" y2="6"/>
                        <line x1="3" y1="10" x2="21" y2="10"/>
                      </svg>
                      {event.date}
                    </div>
                    <div className="event-location">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                        <circle cx="12" cy="10" r="3"/>
                      </svg>
                      {event.location}
                    </div>
                  </div>
                  <div className="event-footer">
                    <div className="event-price">{event.price}</div>
                    <button className="book-btn">
                      <span>Book Now</span>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="m9 18 6-6-6-6"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="view-all">
            <button className="view-all-btn">
              <span>View All Events</span>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="m9 18 6-6-6-6"/>
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="newsletter">
        <div className="container">
          <div className="newsletter-content">
            <div className="newsletter-text">
              <h2>Never miss an event</h2>
              <p>Get early access to tickets and exclusive event recommendations delivered to your inbox.</p>
            </div>
            <div className="newsletter-form">
              <div className="form-group">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="email-input"
                />
                <button className="subscribe-btn">
                  <span>Get Started</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="m9 18 6-6-6-6"/>
                  </svg>
                </button>
              </div>
              <p className="newsletter-disclaimer">
                Join 100,000+ event enthusiasts. Unsubscribe anytime.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
