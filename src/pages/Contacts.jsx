import React, { useState } from 'react';
import './Contacts.css';

const Contacts = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We\'ll get back to you soon.');
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  return (
    <div className="contacts-page">
      <div className="contacts-header">
        <div className="container">
          <h1 className="page-title">Get in Touch</h1>
          <p className="page-subtitle">We'd love to hear from you</p>
        </div>
      </div>

      <div className="container">
        <div className="contacts-content">
          {/* Contact Information */}
          <div className="contact-info">
            <h2>Contact Information</h2>
            <p className="contact-description">
              Have questions about events, tickets, or need support? 
              We're here to help you every step of the way.
            </p>

            <div className="contact-methods">
              <div className="contact-method">
                <div className="contact-icon">📧</div>
                <div className="contact-details">
                  <h3>Email Us</h3>
                  <p><EMAIL></p>
                  <span>We'll respond within 24 hours</span>
                </div>
              </div>

              <div className="contact-method">
                <div className="contact-icon">📞</div>
                <div className="contact-details">
                  <h3>Call Us</h3>
                  <p>+1 (555) 123-4567</p>
                  <span>Mon-Fri, 9AM-6PM EST</span>
                </div>
              </div>

              <div className="contact-method">
                <div className="contact-icon">💬</div>
                <div className="contact-details">
                  <h3>Live Chat</h3>
                  <p>Available on our website</p>
                  <span>24/7 support</span>
                </div>
              </div>

              <div className="contact-method">
                <div className="contact-icon">📍</div>
                <div className="contact-details">
                  <h3>Visit Us</h3>
                  <p>123 Event Street<br />New York, NY 10001</p>
                  <span>By appointment only</span>
                </div>
              </div>
            </div>

            <div className="social-section">
              <h3>Follow Us</h3>
              <div className="social-links">
                <a href="#" className="social-link">📘 Facebook</a>
                <a href="#" className="social-link">🐦 Twitter</a>
                <a href="#" className="social-link">📷 Instagram</a>
                <a href="#" className="social-link">💼 LinkedIn</a>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-form-section">
            <h2>Send us a Message</h2>
            <form className="contact-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name">Full Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Enter your full name"
                />
              </div>

              <div className="form-group">
                <label htmlFor="email">Email Address *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  placeholder="Enter your email address"
                />
              </div>

              <div className="form-group">
                <label htmlFor="subject">Subject *</label>
                <select
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select a subject</option>
                  <option value="general">General Inquiry</option>
                  <option value="support">Technical Support</option>
                  <option value="billing">Billing Question</option>
                  <option value="refund">Refund Request</option>
                  <option value="partnership">Partnership</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="message">Message *</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows="6"
                  placeholder="Tell us how we can help you..."
                ></textarea>
              </div>

              <button type="submit" className="submit-btn">
                Send Message
              </button>
            </form>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="faq-section">
          <h2>Frequently Asked Questions</h2>
          <div className="faq-grid">
            <div className="faq-item">
              <h3>How do I get my tickets?</h3>
              <p>After purchase, you'll receive digital tickets via email and in your TikiVis account. You can also access them through our mobile app.</p>
            </div>
            <div className="faq-item">
              <h3>Can I get a refund?</h3>
              <p>Refund policies vary by event. Check the event details for specific refund terms. Generally, refunds are available up to 24 hours before the event.</p>
            </div>
            <div className="faq-item">
              <h3>Is my payment secure?</h3>
              <p>Yes! We use industry-standard encryption and work with trusted payment processors to ensure your financial information is always protected.</p>
            </div>
            <div className="faq-item">
              <h3>Can I transfer my tickets?</h3>
              <p>Most tickets can be transferred to another person through your TikiVis account. Some events may have restrictions on transfers.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contacts;
