/* Modern Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition-normal);
}

.header-scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-lg);
}

.header-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.nav-brand {
  display: flex;
  align-items: center;
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: -2px;
}

.logo {
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.tagline {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: -2px;
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-8);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--text-primary);
  background: var(--gray-50);
}

.nav-link:active,
.nav-link[href="#home"]:target {
  color: var(--primary-600);
  background: var(--primary-50);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  height: 100%;
}

.login-btn {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-4);
  border-radius: 50px;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  height: 36px;
  min-width: 70px;
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  border-color: rgba(124, 109, 242, 0.4);
  color: var(--primary-600);
  background: rgba(124, 109, 242, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 109, 242, 0.15);
}

.signup-btn {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  border: none;
  color: white;
  padding: var(--space-2) var(--space-5);
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.875rem;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  height: 36px;
  min-width: 100px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(124, 109, 242, 0.3);
}

.signup-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.signup-btn:hover::before {
  left: 100%;
}

.signup-btn:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--secondary-600));
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(124, 109, 242, 0.4);
}

.signup-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(124, 109, 242, 0.3);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  z-index: 10;
}

.mobile-menu-btn:hover {
  background: var(--gray-50);
}

.mobile-menu-btn span {
  width: 20px;
  height: 2px;
  background: var(--text-secondary);
  margin: 2px 0;
  transition: all var(--transition-fast);
  border-radius: 1px;
  transform-origin: center;
}

.mobile-menu-btn.menu-open span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.menu-open span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.menu-open span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .header-container {
    padding: 0 var(--space-4);
  }

  .nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--border-light);
    box-shadow: var(--shadow-xl);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-list {
    flex-direction: column;
    padding: var(--space-6);
    gap: var(--space-2);
  }

  .nav-link {
    display: block;
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 1rem;
  }

  .header-actions {
    gap: var(--space-2);
  }

  .login-btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
    height: 32px;
    min-width: 60px;
  }

  .signup-btn {
    padding: var(--space-2) var(--space-4);
    font-size: 0.8rem;
    height: 32px;
    min-width: 80px;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .tagline {
    display: none;
  }

  .logo {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 var(--space-3);
  }

  .logo-container {
    gap: var(--space-2);
  }

  .header-actions .login-btn {
    display: none;
  }
}
