import React, { useState } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import Events from './pages/Events';
import Contacts from './pages/Contacts';
import Login from './pages/Login';
import Signup from './pages/Signup';
import './App.css';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  // Simple routing function
  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <Home />;
      case 'events':
        return <Events />;
      case 'contacts':
        return <Contacts />;
      case 'login':
        return <Login />;
      case 'signup':
        return <Signup />;
      default:
        return <Home />;
    }
  };

  // Handle navigation
  React.useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1) || 'home';
      setCurrentPage(hash);
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Set initial page

    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  // Check if current page should show header/footer
  const isAuthPage = currentPage === 'login' || currentPage === 'signup';

  return (
    <div className="App">
      {!isAuthPage && <Header />}
      <main className="main-content">
        {renderPage()}
      </main>
      {!isAuthPage && <Footer />}
    </div>
  );
}

export default App;
